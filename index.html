<!DOCTYPE html>
<html>
    <head>
        <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    </head>
    <body>
        <div id="app">
            <span v-html="message"></span>
             <p>Your lucky number is {{luckyNumber}}</p>
            <p>Your lucky number is {{luckyNumber%2==0 ? "even  number" : "odd number"}}</p>
            <p v-bind:class="maintTitle">Your lucky number is {{luckyNumber}}</p>
       
            <input @keyup.alt.enter="clear" />

            <button @click="displayLuckyNumber">Get Lucky Number <p v-if="clickedLuckyNumber !== null">{{clickedLuckyNumber}}</p></button>
            
        </div>
    </body>
    <script>
        const {createApp, ref} = Vue;
        createApp({
            setup() {
                const message = "Hello Vue 4";
                let luckyNumber = ref(6);
                const clickedLuckyNumber = ref(null);
                const maintTitle = {h1: true, bgGreen: true};

                function getLuckyNumber() {

                    return Math.floor(Math.random() * 10);
                }

                function clear() {
                    //clear the content of input field
                    
                    
                }
                

                function getBackgroundGreen() {
                    array.forEach(element => {
                        
                    });
                    maintTitle.bgGreen = !maintTitle.bgGreen;
                }

                function displayLuckyNumber() {
                    clickedLuckyNumber.value = getLuckyNumber();
                    luckyNumber.value = clickedLuckyNumber.value
                }

                function getMessage() {
                    return '<h1>' + message + '</h1>';
                }

                return {
                    message,
                    luckyNumber,
                    clickedLuckyNumber,
                    displayLuckyNumber,
                    getMessage,
                    maintTitle
                }
            }
        }).mount("#app");
    </script>
    <style>
        .bgGreen {
            background-color: green;
        }
        .h1 {
            color: blue;
            font-size: 20px;
        }
    </style>
</html>

